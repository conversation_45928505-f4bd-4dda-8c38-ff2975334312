import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Send, MessageCircle, BookOpen, FileText, Trash2, Search } from 'lucide-react';
import { useTutorialChat } from '@/hooks/useTutorialChat';
import { Tutorial } from '@/types';


interface TutorialChatInterfaceProps {
  availableTutorials: Tutorial[];
  isLoading?: boolean;
}

const TutorialChatInterface: React.FC<TutorialChatInterfaceProps> = ({
  availableTutorials,
  isLoading = false,
}) => {
  const [inputValue, setInputValue] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    selectedTutorial,
    messages,
    isTyping,
    error,
    selectTutorial,
    sendMessage,
    clearChat,
  } = useTutorialChat();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || !selectedTutorial) return;

    await sendMessage(inputValue);
    setInputValue('');
  };

  const handleTutorialSelect = (tutorialId: string) => {
    const tutorial = availableTutorials.find(t => t.id === tutorialId);
    if (tutorial) {
      selectTutorial(tutorial);
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const filteredTutorials = availableTutorials.filter(tutorial =>
    tutorial.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tutorial.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getTutorialIcon = (type: string) => {
    return type === 'interactive_tutorial' ? (
      <BookOpen className="h-4 w-4 text-green-600" />
    ) : (
      <FileText className="h-4 w-4 text-blue-600" />
    );
  };

  const getTutorialTypeLabel = (type: string) => {
    return type === 'interactive_tutorial' ? 'Interactive Tutorial' : 'Documentation';
  };

  return (
    <div className="flex flex-col h-full max-w-4xl mx-auto">
      {/* Tutorial Selection Header */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Chat with Tutorial
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search tutorials..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Tutorial Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Select a tutorial to chat about:</label>
            <Select onValueChange={handleTutorialSelect} disabled={isLoading}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a tutorial..." />
              </SelectTrigger>
              <SelectContent>
                {filteredTutorials.length === 0 ? (
                  <SelectItem value="no-tutorials" disabled>
                    {searchTerm ? 'No tutorials match your search' : 'No tutorials available'}
                  </SelectItem>
                ) : (
                  filteredTutorials.map((tutorial) => (
                    <SelectItem key={tutorial.id} value={tutorial.id}>
                      <div className="flex items-center gap-2 w-full">
                        {getTutorialIcon(tutorial.type)}
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">{tutorial.title}</div>
                          <div className="text-xs text-gray-500 truncate">
                            {getTutorialTypeLabel(tutorial.type)}
                            {tutorial.difficulty && ` • ${tutorial.difficulty}`}
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Selected Tutorial Info */}
          {selectedTutorial && (
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                {getTutorialIcon(selectedTutorial.type)}
                <div>
                  <div className="font-medium">{selectedTutorial.title}</div>
                  <div className="text-sm text-gray-600">{selectedTutorial.description}</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  {getTutorialTypeLabel(selectedTutorial.type)}
                </Badge>
                {selectedTutorial.difficulty && (
                  <Badge variant="outline">{selectedTutorial.difficulty}</Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearChat}
                  className="text-gray-500 hover:text-red-600"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Chat Interface */}
      {selectedTutorial && (
        <Card className="flex-1 flex flex-col">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">
              Chat about "{selectedTutorial.title}"
            </CardTitle>
          </CardHeader>
          
          <CardContent className="flex-1 p-0 overflow-hidden">
            <ScrollArea className="h-full">
              <div className="px-6 py-4 space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg px-4 py-3 ${
                        message.sender === 'user'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted'
                      }`}
                    >
                      <p className="text-sm leading-relaxed">{message.text}</p>
                      <p className="text-xs opacity-70 mt-2">
                        {formatTime(message.timestamp)}
                      </p>
                    </div>
                  </div>
                ))}
                
                {isTyping && (
                  <div className="flex justify-start">
                    <div className="bg-muted rounded-lg px-4 py-3">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                )}
                
                {error && (
                  <div className="flex justify-center">
                    <div className="bg-red-50 text-red-600 rounded-lg px-4 py-2 text-sm">
                      {error}
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
          </CardContent>

          <CardFooter className="p-6 pt-3">
            <form onSubmit={handleSendMessage} className="flex w-full space-x-3">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="Ask a question about this tutorial..."
                className="flex-1"
                disabled={isTyping}
              />
              <Button 
                type="submit" 
                disabled={!inputValue.trim() || isTyping}
                className="px-6"
              >
                <Send className="h-4 w-4" />
              </Button>
            </form>
          </CardFooter>
        </Card>
      )}

      {/* Empty State */}
      {!selectedTutorial && (
        <Card className="flex-1 flex items-center justify-center">
          <CardContent className="text-center py-12">
            <MessageCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Select a Tutorial to Start Chatting
            </h3>
            <p className="text-gray-600 max-w-md">
              Choose a tutorial from the dropdown above to start an interactive conversation. 
              Ask questions, get explanations, and dive deeper into the concepts.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TutorialChatInterface;
