# Daytona Sandbox Integration

This document explains how to set up and use the real Daytona sandbox integration in CodeTutorPro.

## Overview

The Daytona service provides both **demo mode** and **real API integration** capabilities:

- **Demo Mode**: Simulates code execution locally for development and testing
- **Real Mode**: Uses actual Daytona sandboxes for secure, isolated code execution

## Setup

### 1. Get Daytona API Key

1. Visit [Daytona](https://app.daytona.io/)
2. Sign up or log in to your account
3. Navigate to your account settings
4. Generate an API key

### 2. Configure Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your Daytona API key:
   ```env
   DAYTONA_API_KEY=your_actual_api_key_here
   DAYTONA_API_URL=https://app.daytona.io/api
   DAYTONA_TARGET=us
   ```

### 3. Install Dependencies

The Daytona TypeScript SDK is already included in the project dependencies:

```bash
npm install
```

## Usage

### Basic Usage

```typescript
import { daytonaService } from '../services/daytonaService';

// Initialize the service
const initialized = await daytonaService.initialize();

// Create a sandbox
const sandbox = await daytonaService.createSandbox('javascript', {
  cpu: 1,
  memory: 2,
  disk: 10
});

// Execute code
const result = await daytonaService.executeCode(
  'console.log("Hello World!");',
  'javascript'
);

console.log(result.output); // "Hello World!"
```

### Advanced Usage

```typescript
// Create sandbox with custom resources and environment variables
const sandbox = await daytonaService.createSandbox('python', {
  cpu: 2,
  memory: 4,
  disk: 20
}, {
  PYTHON_ENV: 'development',
  DEBUG: 'true'
});

// Execute Python code with matplotlib charts
const result = await daytonaService.executeCode(`
import matplotlib.pyplot as plt
import numpy as np

x = np.linspace(0, 10, 100)
y = np.sin(x)

plt.figure(figsize=(10, 6))
plt.plot(x, y)
plt.title('Sine Wave')
plt.xlabel('X')
plt.ylabel('Y')
plt.show()
`, 'python');

// Access chart data if available
if (result.charts && result.charts.length > 0) {
  console.log('Generated charts:', result.charts);
}
```

## Supported Languages

The Daytona service supports multiple programming languages:

- **JavaScript/Node.js**: `javascript`
- **Python**: `python`
- **TypeScript**: `typescript`
- **Java**: `java`
- **C++**: `cpp`

## API Reference

### DaytonaService Methods

#### `initialize(apiKey?, apiUrl?, target?)`
Initialize the Daytona client with optional configuration.

#### `createSandbox(language, resources?, envVars?)`
Create a new sandbox with specified language and optional resource allocation.

#### `executeCode(code, language, timeout?)`
Execute code in the active sandbox with optional timeout.

#### `getSandboxInfo()`
Get detailed information about the current sandbox.

#### `stopSandbox()`
Stop the current sandbox (preserves state).

#### `startSandbox()`
Start a stopped sandbox.

#### `cleanup(deleteSandbox?)`
Clean up resources, optionally deleting the sandbox.

#### `getPreviewUrl(port)`
Get a preview URL for a specific port in the sandbox.

#### `getStatus()`
Get the current service status.

## Demo Mode vs Real Mode

### Demo Mode
- Activated when no API key is provided
- Simulates code execution locally
- Useful for development and testing
- No actual sandboxes are created

### Real Mode
- Activated when valid API key is provided
- Uses actual Daytona sandboxes
- Secure, isolated execution environment
- Supports all Daytona features

## Error Handling

The service includes robust error handling:

1. **Graceful Fallback**: If real API fails, automatically falls back to demo mode
2. **Detailed Logging**: Comprehensive error logging for debugging
3. **Timeout Handling**: Configurable timeouts for all operations
4. **Resource Cleanup**: Automatic cleanup on errors

## Testing

Run the test suite to verify your Daytona integration:

```bash
# Run the Daytona service test
npx ts-node src/tests/daytonaServiceTest.ts
```

This will test:
- Service initialization
- Sandbox creation
- Code execution in multiple languages
- Resource management
- Error handling

## Best Practices

1. **Resource Management**: Always clean up sandboxes when done
2. **Error Handling**: Handle both demo and real mode scenarios
3. **Timeouts**: Set appropriate timeouts for long-running operations
4. **Security**: Never expose API keys in client-side code
5. **Monitoring**: Monitor sandbox usage and costs

## Troubleshooting

### Common Issues

1. **API Key Invalid**: Verify your API key is correct and active
2. **Network Issues**: Check your internet connection and firewall settings
3. **Resource Limits**: Ensure you haven't exceeded your Daytona account limits
4. **Timeout Errors**: Increase timeout values for complex operations

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

This will provide detailed logs of all Daytona API interactions.

## Cost Considerations

- Daytona charges based on sandbox usage time and resources
- Use auto-stop and auto-archive features to minimize costs
- Monitor your usage through the Daytona dashboard
- Consider using demo mode for development to reduce costs

## Support

For issues related to:
- **Daytona API**: Contact [Daytona Support](https://www.daytona.io/support)
- **Integration Code**: Create an issue in this repository
