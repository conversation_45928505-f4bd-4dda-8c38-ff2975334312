/**
 * Test specific examples for Daytona Service
 */

import { daytonaService } from './dist/daytonaService.js';

async function testSpecificExamples() {
  console.log('🧪 Testing Daytona Service with Specific Examples');
  console.log('=================================================');

  try {
    // Initialize service
    await daytonaService.initialize();
    await daytonaService.createSandbox('javascript');

    // Test predefined examples that should return specific outputs
    const examples = [
      {
        name: 'Hello World JavaScript',
        code: 'console.log("Hello World!");',
        language: 'javascript',
        expectedOutput: 'Hello World!'
      },
      {
        name: 'Hello from Manus AI',
        code: 'console.log("Hello from Manus AI!");',
        language: 'javascript',
        expectedOutput: 'Hello from Manus AI!'
      },
      {
        name: 'Simple Math',
        code: 'const sum = (a, b) => a + b;\nconsole.log(sum(2, 3));',
        language: 'javascript',
        expectedOutput: '5'
      },
      {
        name: 'Math PI',
        code: 'console.log(Math.PI);',
        language: 'javascript',
        expectedOutput: '3.141592653589793'
      },
      {
        name: 'Current Year',
        code: 'console.log(new Date().getFullYear());',
        language: 'javascript',
        expectedOutput: new Date().getFullYear().toString()
      },
      {
        name: 'Python Hello World',
        code: 'print("Hello World!")',
        language: 'python',
        expectedOutput: 'Hello World!'
      },
      {
        name: 'Python Math',
        code: 'print(2 + 3)',
        language: 'python',
        expectedOutput: '5'
      },
      {
        name: 'Python List Comprehension',
        code: 'print([i**2 for i in range(5)])',
        language: 'python',
        expectedOutput: '[0, 1, 4, 9, 16]'
      },
      {
        name: 'TypeScript Hello',
        code: 'console.log("Hello TypeScript!");',
        language: 'typescript',
        expectedOutput: 'Hello TypeScript!'
      },
      {
        name: 'TypeScript with Types',
        code: 'const greeting: string = "Hello";\nconsole.log(greeting);',
        language: 'typescript',
        expectedOutput: 'Hello'
      }
    ];

    let passedTests = 0;
    let totalTests = examples.length;

    for (let i = 0; i < examples.length; i++) {
      const example = examples[i];
      console.log(`\n${i + 1}. Testing: ${example.name}`);
      console.log(`   Language: ${example.language}`);
      console.log(`   Code: ${example.code.replace(/\n/g, '\\n')}`);
      
      const result = await daytonaService.executeCode(example.code, example.language);
      
      console.log(`   Execution: ${result.success ? 'SUCCESS' : 'FAILED'}`);
      console.log(`   Output: "${result.output}"`);
      console.log(`   Expected: "${example.expectedOutput}"`);
      
      const outputMatches = result.output === example.expectedOutput;
      console.log(`   Match: ${outputMatches ? '✅ PASS' : '❌ FAIL'}`);
      
      if (outputMatches) {
        passedTests++;
      }
      
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    }

    console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All predefined examples work correctly!');
    } else {
      console.log('⚠️  Some examples may need adjustment');
    }

    // Test unknown code (should return generic message)
    console.log('\n🔍 Testing unknown code...');
    const unknownResult = await daytonaService.executeCode(
      'console.log("This is a custom message");',
      'javascript'
    );
    
    console.log(`   Output: "${unknownResult.output}"`);
    console.log(`   Should be generic: ${unknownResult.output.includes('demo mode') ? '✅ CORRECT' : '❌ UNEXPECTED'}`);

    // Cleanup
    await daytonaService.cleanup();

  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
  }

  console.log('\n✅ Specific examples test completed!');
}

// Run the test
testSpecificExamples().catch(console.error);
