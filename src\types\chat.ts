// Chat-related type definitions for the Chat with Tutorial feature

import { Tutorial } from ".";

export interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
  tutorialContext?: {
    tutorialId: string;
    tutorialTitle: string;
    relevantSection?: string;
  };
}

export interface TutorialChatSession {
  id: string;
  tutorialId: string;
  tutorialTitle: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}



export interface ChatState {
  selectedTutorial: Tutorial | null;
  messages: ChatMessage[];
  isLoading: boolean;
  isTyping: boolean;
  error: string | null;
}

export interface ChatActions {
  selectTutorial: (tutorial: Tutorial) => void;
  sendMessage: (message: string) => Promise<void>;
  clearChat: () => void;
  loadChatHistory: (tutorialId: string) => Promise<void>;
}
