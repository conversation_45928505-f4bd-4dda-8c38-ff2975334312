/**
 * Simple test for Daytona Service
 */

import { daytonaService } from './dist/daytonaService.js';

async function testDaytonaService() {
  console.log('🚀 Testing Daytona Service Integration');
  console.log('=====================================');

  try {
    // Test 1: Initialize service
    console.log('\n1. Initializing Daytona service...');
    const initialized = await daytonaService.initialize();
    console.log(`   Initialization result: ${initialized ? 'SUCCESS (Real API)' : 'DEMO MODE'}`);
    
    const status = daytonaService.getStatus();
    console.log(`   Status: initialized=${status.initialized}, isDemo=${status.isDemo}, hasSandbox=${status.hasSandbox}`);

    // Test 2: Create sandbox
    console.log('\n2. Creating sandbox...');
    const sandboxResult = await daytonaService.createSandbox('javascript', {
      cpu: 1,
      memory: 2,
      disk: 10
    });
    
    console.log(`   Sandbox creation: ${sandboxResult.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`   Sandbox ID: ${sandboxResult.sandboxId}`);
    console.log(`   Message: ${sandboxResult.message}`);

    if (sandboxResult.error) {
      console.log(`   Error: ${sandboxResult.error}`);
    }

    // Test 3: Get sandbox info
    console.log('\n3. Getting sandbox info...');
    const sandboxInfo = await daytonaService.getSandboxInfo();
    if (sandboxInfo) {
      console.log(`   ID: ${sandboxInfo.id}`);
      console.log(`   State: ${sandboxInfo.state}`);
      console.log(`   Resources: ${sandboxInfo.resources.cpu}, ${sandboxInfo.resources.memory}`);
      console.log(`   Created: ${sandboxInfo.createdAt}`);
    } else {
      console.log('   No sandbox info available');
    }

    // Test 4: Execute JavaScript code
    console.log('\n4. Executing JavaScript code...');
    const jsResult = await daytonaService.executeCode(
      'console.log("Hello from Daytona sandbox!");',
      'javascript'
    );
    
    console.log(`   Execution: ${jsResult.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`   Output: ${jsResult.output}`);
    console.log(`   Execution time: ${jsResult.executionTime}ms`);
    console.log(`   Demo mode: ${jsResult.isDemo}`);
    
    if (jsResult.error) {
      console.log(`   Error: ${jsResult.error}`);
    }

    // Test 5: Execute Python code
    console.log('\n5. Executing Python code...');
    const pythonResult = await daytonaService.executeCode(
      'print("Hello from Python in Daytona!")\nprint(f"2 + 3 = {2 + 3}")',
      'python'
    );
    
    console.log(`   Execution: ${pythonResult.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`   Output: ${pythonResult.output}`);
    console.log(`   Execution time: ${pythonResult.executionTime}ms`);
    console.log(`   Demo mode: ${pythonResult.isDemo}`);
    
    if (pythonResult.error) {
      console.log(`   Error: ${pythonResult.error}`);
    }

    // Test 6: Execute complex JavaScript with math
    console.log('\n6. Executing complex JavaScript...');
    const complexJsResult = await daytonaService.executeCode(`
      const numbers = [1, 2, 3, 4, 5];
      const sum = numbers.reduce((a, b) => a + b, 0);
      const average = sum / numbers.length;
      console.log('Numbers: ' + numbers.join(', '));
      console.log('Sum: ' + sum);
      console.log('Average: ' + average);
    `, 'javascript');
    
    console.log(`   Execution: ${complexJsResult.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`   Output: ${complexJsResult.output}`);
    console.log(`   Demo mode: ${complexJsResult.isDemo}`);

    // Test 7: Test TypeScript code
    console.log('\n7. Executing TypeScript code...');
    const tsResult = await daytonaService.executeCode(
      'const greeting: string = "Hello TypeScript!";\nconsole.log(greeting);',
      'typescript'
    );
    
    console.log(`   Execution: ${tsResult.success ? 'SUCCESS' : 'FAILED'}`);
    console.log(`   Output: ${tsResult.output}`);
    console.log(`   Demo mode: ${tsResult.isDemo}`);

    // Test 8: Test service ready status
    console.log('\n8. Checking service status...');
    const isReady = daytonaService.isReady();
    console.log(`   Service ready: ${isReady}`);

    // Test 9: Test preview URL (if real sandbox)
    if (!status.isDemo) {
      console.log('\n9. Getting preview URL...');
      const previewUrl = await daytonaService.getPreviewUrl(3000);
      console.log(`   Preview URL: ${previewUrl}`);
    } else {
      console.log('\n9. Skipping preview URL test (demo mode)');
    }

    // Test 10: Cleanup
    console.log('\n10. Cleaning up...');
    await daytonaService.cleanup(false); // Don't delete sandbox for now
    console.log('    Cleanup completed');

  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
    console.error('Stack trace:', error.stack);
  }

  console.log('\n✅ Daytona Service test completed!');
  console.log('\n📝 Summary:');
  console.log('   - If you see "DEMO MODE", the service is working but using simulated execution');
  console.log('   - To use real Daytona sandboxes, set DAYTONA_API_KEY in your .env file');
  console.log('   - See docs/DAYTONA_INTEGRATION.md for setup instructions');
}

// Run the test
testDaytonaService().catch(console.error);
