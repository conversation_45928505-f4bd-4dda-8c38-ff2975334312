
import { useState, useEffect, useRef, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import useUserSessionStore from '@/stores/useUserStore';

interface SubscriptionData {
  subscribed: boolean;
  subscription_tier?: string;
  subscription_end?: string;
  trial_end?: string;
  in_trial?: boolean;
}

// Global request deduplication to prevent multiple simultaneous calls
let globalSubscriptionPromise: Promise<any> | null = null;
let lastSuccessfulCheck: number = 0;
const CACHE_DURATION = 30000; // 30 seconds cache

export const useSubscription = () => {
  const { user, session } = useAuth();
  const { subscriptionInfo, setSubscriptionInfo } = useUserSessionStore();

  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData>({
    subscribed: subscriptionInfo.subscribed || false,
    subscription_tier: subscriptionInfo.subscription_tier,
    subscription_end: subscriptionInfo.subscription_end,
    trial_end: subscriptionInfo.trial_end,
    in_trial: subscriptionInfo.in_trial || false
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const isCheckingRef = useRef(false);
  const retryCountRef = useRef(0);
  const maxRetries = 3;

  const checkSubscription = useCallback(async (forceRefresh = false) => {
    if (!session?.access_token) {
      console.log('No access token available for subscription check');
      setLoading(false);
      return;
    }

    // Check cache first (unless force refresh)
    const now = Date.now();
    if (!forceRefresh && (now - lastSuccessfulCheck) < CACHE_DURATION && subscriptionData.subscribed !== undefined) {
      console.log('Using cached subscription data');
      setLoading(false);
      return;
    }

    // Prevent multiple simultaneous calls
    if (isCheckingRef.current) {
      console.log('Subscription check already in progress, skipping...');
      return;
    }

    // Use global promise deduplication
    if (globalSubscriptionPromise && !forceRefresh) {
      console.log('Using existing subscription check promise...');
      try {
        const result = await globalSubscriptionPromise;
        updateSubscriptionData(result);
        setError(null);
        lastSuccessfulCheck = Date.now();
        retryCountRef.current = 0;
      } catch (err) {
        console.error('Error from shared subscription check:', err);
        handleSubscriptionError(err);
      } finally {
        setLoading(false);
      }
      return;
    }

    try {
      setLoading(true);
      isCheckingRef.current = true;
      console.log('Checking subscription status...');

      // Create the promise and store it globally
      globalSubscriptionPromise = supabase.functions.invoke('check-subscription', {
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      const { data, error } = await globalSubscriptionPromise;

      if (error) throw error;

      console.log('Subscription check successful:', data);
      updateSubscriptionData(data);
      setError(null);
      lastSuccessfulCheck = Date.now();
      retryCountRef.current = 0;
    } catch (err) {
      console.error('Error checking subscription:', err);
      handleSubscriptionError(err);
    } finally {
      setLoading(false);
      isCheckingRef.current = false;
      globalSubscriptionPromise = null; // Clear the global promise
    }
  }, [session?.access_token, subscriptionData.subscribed]);

  const updateSubscriptionData = useCallback((data: SubscriptionData) => {
    setSubscriptionData(data);
    // Sync with user store
    setSubscriptionInfo({
      subscribed: data.subscribed,
      subscription_tier: data.subscription_tier,
      subscription_end: data.subscription_end,
      trial_end: data.trial_end,
      in_trial: data.in_trial || false
    });
  }, [setSubscriptionInfo]);

  const handleSubscriptionError = useCallback((err: any) => {
    const errorMessage = err instanceof Error ? err.message : 'Failed to check subscription';
    setError(errorMessage);

    // Implement retry logic for network errors
    if (retryCountRef.current < maxRetries && (
      errorMessage.includes('network') ||
      errorMessage.includes('timeout') ||
      errorMessage.includes('fetch')
    )) {
      retryCountRef.current++;
      console.log(`Retrying subscription check (attempt ${retryCountRef.current}/${maxRetries})`);
      setTimeout(() => checkSubscription(true), 2000 * retryCountRef.current); // Exponential backoff
    }
  }, [checkSubscription]);

  // Initialize subscription data from store on mount
  useEffect(() => {
    if (subscriptionInfo.subscribed !== undefined) {
      setSubscriptionData({
        subscribed: subscriptionInfo.subscribed,
        subscription_tier: subscriptionInfo.subscription_tier,
        subscription_end: subscriptionInfo.subscription_end,
        trial_end: subscriptionInfo.trial_end,
        in_trial: subscriptionInfo.in_trial || false
      });
    }
  }, [subscriptionInfo]);

  useEffect(() => {
    if (user && session) {
      checkSubscription();
    } else {
      console.log('No user or session, setting loading to false');
      setLoading(false);
      // Reset subscription data when no user
      setSubscriptionData({
        subscribed: false,
        in_trial: false
      });
    }
  }, [user, session, checkSubscription]);

  // Debug logging
  useEffect(() => {
    console.log('Subscription state updated:', {
      subscribed: subscriptionData.subscribed,
      loading,
      error,
      subscription_tier: subscriptionData.subscription_tier,
      in_trial: subscriptionData.in_trial
    });
  }, [subscriptionData, loading, error]);

  return {
    ...subscriptionData,
    loading,
    error,
    refetchSubscription: (forceRefresh = true) => checkSubscription(forceRefresh),
    // Add debugging helpers
    debugInfo: {
      lastSuccessfulCheck: new Date(lastSuccessfulCheck).toISOString(),
      retryCount: retryCountRef.current,
      isChecking: isCheckingRef.current,
      cacheValid: (Date.now() - lastSuccessfulCheck) < CACHE_DURATION
    }
  };
};
