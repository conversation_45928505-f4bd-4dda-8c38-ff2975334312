// src/Agents/Code2Documentation/pangeaflow/tests/events.test.ts

import {
  eventEmitter,
  EventType,
  emitProgress,
  emitGraphStatus,
  emitError,
  emitComplete,
  onProgress,
  onStatus,
  onError,
  onComplete,
  removeAllListeners
} from '../../shared/events';

describe('Browser-Compatible Event System Tests', () => {
  beforeEach(() => {
    // Clean up all listeners before each test
    removeAllListeners();
  });

  afterEach(() => {
    // Clean up all listeners after each test
    removeAllListeners();
  });

  test('Event emitter should be defined and functional', () => {
    expect(eventEmitter).toBeDefined();
    expect(typeof eventEmitter.emit).toBe('function');
    expect(typeof eventEmitter.on).toBe('function');
    expect(typeof eventEmitter.off).toBe('function');
  });

  test('Progress events should work correctly', (done) => {
    const testComponent = 'TestComponent';
    const testProgress = 50;
    const testMessage = 'Test progress message';

    onProgress((event) => {
      expect(event.component).toBe(testComponent);
      expect(event.progress).toBe(testProgress);
      expect(event.message).toBe(testMessage);
      done();
    });

    emitProgress(testComponent, testProgress, testMessage);
  });

  test('Status events should work correctly', (done) => {
    const testComponent = 'TestComponent';
    const testMessage = 'Test status message';

    onStatus((event) => {
      expect(event.component).toBe(testComponent);
      expect(event.status).toBe('running');
      expect(event.message).toBe(testMessage);
      done();
    });

    emitGraphStatus(testComponent, 25, testMessage);
  });

  test('Error events should work correctly', (done) => {
    const testComponent = 'TestComponent';
    const testMessage = 'Test error message';
    const testError = new Error('Test error');

    onError((event) => {
      expect(event.component).toBe(testComponent);
      expect(event.message).toBe(testMessage);
      expect(event.error).toBe(testError);
      done();
    });

    emitError(testComponent, testMessage, testError);
  });

  test('Complete events should work correctly', (done) => {
    const testComponent = 'TestComponent';
    const testResult = { success: true, data: 'test' };

    onComplete((event) => {
      expect(event.component).toBe(testComponent);
      expect(event.result).toBe(testResult);
      done();
    });

    emitComplete(testComponent, testResult);
  });

  test('Multiple listeners should all receive events', () => {
    const testComponent = 'TestComponent';
    const testProgress = 75;
    const testMessage = 'Test message';

    let listener1Called = false;
    let listener2Called = false;

    onProgress(() => {
      listener1Called = true;
    });

    onProgress(() => {
      listener2Called = true;
    });

    emitProgress(testComponent, testProgress, testMessage);

    expect(listener1Called).toBe(true);
    expect(listener2Called).toBe(true);
  });

  test('Event listener removal should work', () => {
    const testComponent = 'TestComponent';
    const testProgress = 100;
    const testMessage = 'Test message';

    let listenerCalled = false;

    const listener = () => {
      listenerCalled = true;
    };

    // Add listener
    eventEmitter.on(EventType.PROGRESS, listener);

    // Remove listener
    eventEmitter.off(EventType.PROGRESS, listener);

    // Emit event
    emitProgress(testComponent, testProgress, testMessage);

    // Listener should not have been called
    expect(listenerCalled).toBe(false);
  });

  test('removeAllListeners should clear all listeners', () => {
    let progressCalled = false;
    let statusCalled = false;

    onProgress(() => {
      progressCalled = true;
    });

    onStatus(() => {
      statusCalled = true;
    });

    // Remove all listeners
    removeAllListeners();

    // Emit events
    emitProgress('Test', 50, 'Test');
    emitGraphStatus('Test', 50, 'Test');

    // No listeners should have been called
    expect(progressCalled).toBe(false);
    expect(statusCalled).toBe(false);
  });

  test('Error in listener should not break other listeners', () => {
    let goodListenerCalled = false;

    // Add a listener that throws an error
    onProgress(() => {
      throw new Error('Test error in listener');
    });

    // Add a good listener
    onProgress(() => {
      goodListenerCalled = true;
    });

    // Emit event - should not throw
    expect(() => {
      emitProgress('Test', 50, 'Test');
    }).not.toThrow();

    // Good listener should still have been called
    expect(goodListenerCalled).toBe(true);
  });

  test('Event types enum should be defined', () => {
    expect(EventType.PROGRESS).toBe('progress');
    expect(EventType.STATUS).toBe('status');
    expect(EventType.ERROR).toBe('error');
    expect(EventType.COMPLETE).toBe('complete');
  });
});
