import { useState, useCallback } from 'react';
import { ChatMessage,  ChatState, ChatActions } from '@/types/chat';
import { Tutorial } from '@/types';

export const useTutorialChat = (): ChatState & ChatActions => {
  const [selectedTutorial, setSelectedTutorial] = useState<Tutorial | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const selectTutorial = useCallback((tutorial: Tutorial) => {
    setSelectedTutorial(tutorial);
    setMessages([
      {
        id: '1',
        text: `Hello! I'm here to help you understand "${tutorial.title}". Feel free to ask me any questions about this tutorial, its concepts, code examples, or anything else you'd like to learn!`,
        sender: 'assistant',
        timestamp: new Date(),
        tutorialContext: {
          tutorialId: tutorial.id,
          tutorialTitle: tutorial.title,
        },
      },
    ]);
    setError(null);
  }, []);

  const sendMessage = useCallback(async (messageText: string) => {
    if (!selectedTutorial) {
      setError('Please select a tutorial first');
      return;
    }

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: messageText,
      sender: 'user',
      timestamp: new Date(),
      tutorialContext: {
        tutorialId: selectedTutorial.id,
        tutorialTitle: selectedTutorial.title,
      },
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);
    setError(null);

    try {
      // Simulate AI response - replace with actual AI integration later
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: generateMockResponse(messageText, selectedTutorial),
        sender: 'assistant',
        timestamp: new Date(),
        tutorialContext: {
          tutorialId: selectedTutorial.id,
          tutorialTitle: selectedTutorial.title,
        },
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (err) {
      setError('Failed to send message. Please try again.');
      console.error('Chat error:', err);
    } finally {
      setIsTyping(false);
    }
  }, [selectedTutorial]);

  const clearChat = useCallback(() => {
    setMessages([]);
    setSelectedTutorial(null);
    setError(null);
    setIsTyping(false);
  }, []);

  const loadChatHistory = useCallback(async (tutorialId: string) => {
    setIsLoading(true);
    try {
      // TODO: Implement chat history loading from backend
      console.log('Loading chat history for tutorial:', tutorialId);
    } catch (err) {
      setError('Failed to load chat history');
      console.error('Load history error:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    selectedTutorial,
    messages,
    isLoading,
    isTyping,
    error,
    selectTutorial,
    sendMessage,
    clearChat,
    loadChatHistory,
  };
};

// Mock response generator - replace with actual AI integration
function generateMockResponse(userMessage: string, tutorial: Tutorial): string {
  const responses = [
    `Great question about "${tutorial.title}"! Let me explain that concept in detail...`,
    `That's an interesting point regarding this ${tutorial.title}. Here's what you need to know...`,
    `I can help clarify that part of the tutorial. The key concept here is...`,
    `Based on the "${tutorial.title}" tutorial, here's how that works...`,
    `Let me break down that section for you step by step...`,
  ];

  const randomResponse = responses[Math.floor(Math.random() * responses.length)];
  
  // Add some context-aware responses
  if (userMessage.toLowerCase().includes('code')) {
    return `${randomResponse} When looking at the code examples in this tutorial, you'll notice that...`;
  }
  
  if (userMessage.toLowerCase().includes('error') || userMessage.toLowerCase().includes('problem')) {
    return `${randomResponse} This is a common issue that many developers encounter. Here's how to solve it...`;
  }
  
  if (userMessage.toLowerCase().includes('explain') || userMessage.toLowerCase().includes('how')) {
    return `${randomResponse} Let me walk you through this concept with a practical example...`;
  }

  return randomResponse;
}
