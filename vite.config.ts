import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  
  // build: {
  //   cssCodeSplit: true,
  //   minify: "esbuild",
  //   sourcemap: false,
  //   target: 'esnext',
  //   chunkSizeWarningLimit: 1000,
  //   rollupOptions: {
  //     output: {
  //       manualChunks: (id) => {
  //         // Core React libraries - keep these together for better caching
  //         if (id.includes('react') || id.includes('react-dom') || id.includes('react-router-dom')) {
  //           return 'vendor-react';
  //         }

  //         // Heavy libraries that should be separate chunks
  //         if (id.includes('framer-motion')) {
  //           return 'vendor-framer';
  //         }

  //         if (id.includes('mermaid')) {
  //           return 'vendor-mermaid';
  //         }

  //         if (id.includes('canvas')) {
  //           return 'vendor-canvas';
  //         }

  //         if (id.includes('highlight.js')) {
  //           return 'vendor-highlight';
  //         }

  //         if (id.includes('openai')) {
  //           return 'vendor-openai';
  //         }

  //         if (id.includes('@clerk/clerk-react')) {
  //           return 'vendor-clerk';
  //         }

  //         if (id.includes('@supabase/supabase-js')) {
  //           return 'vendor-supabase';
  //         }

  //         if (id.includes('@tanstack/react-query') || id.includes('@tanstack/react-table')) {
  //           return 'vendor-tanstack';
  //         }

  //         // All Radix UI components in one chunk
  //         if (id.includes('@radix-ui/')) {
  //           return 'vendor-radix';
  //         }

  //         // Markdown and syntax highlighting
  //         if (id.includes('react-markdown') || id.includes('react-syntax-highlighter') ||
  //             id.includes('rehype') || id.includes('remark')) {
  //           return 'vendor-markdown';
  //         }

  //         // UI components and utilities
  //         if (id.includes('shadcn/ui') || id.includes('components/ui') ||
  //             id.includes('lucide-react') || id.includes('class-variance-authority')) {
  //           return 'ui';
  //         }

  //         // Utility libraries
  //         if (id.includes('zod') || id.includes('zustand') || id.includes('date-fns') ||
  //             id.includes('clsx') || id.includes('tailwind-merge')) {
  //           return 'utils';
  //         }

  //         // Node modules that aren't specifically chunked above
  //         if (id.includes('node_modules')) {
  //           return 'vendor-misc';
  //         }
  //       }
  //     },
  //     external: [
  //       /\.md$/,
  //       /^test\//,  // Exclude the test/ folder
  //       /^scripts\//,  
  //       /^docs\//,  
  //       /^src\/examples\//,
  //     ]
  //   }
  // }
}));
