import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useSubscription } from '@/hooks/useSubscription';
import { useAuth } from '@/hooks/useAuth';
import useUserSessionStore from '@/stores/useUserStore';

const SubscriptionStatusTest = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const { 
    subscribed, 
    loading, 
    error, 
    refetchSubscription, 
    debugInfo,
    subscription_tier,
    in_trial 
  } = useSubscription();
  const { user, session } = useAuth();
  const { subscriptionInfo } = useUserSessionStore();

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const runTests = async () => {
    setTestResults([]);
    addResult('Starting subscription status tests...');

    // Test 1: Check if user is authenticated
    if (!user || !session) {
      addResult('❌ User not authenticated - please sign in first');
      return;
    }
    addResult('✅ User authenticated');

    // Test 2: Check subscription hook state
    addResult(`📊 Hook State - Subscribed: ${subscribed}, Loading: ${loading}, Error: ${error || 'None'}`);
    addResult(`📊 Store State - Subscribed: ${subscriptionInfo.subscribed}, Tier: ${subscriptionInfo.subscription_tier || 'None'}`);

    // Test 3: Force refresh subscription
    addResult('🔄 Testing subscription refresh...');
    try {
      await refetchSubscription(true);
      addResult('✅ Subscription refresh completed');
    } catch (err) {
      addResult(`❌ Subscription refresh failed: ${err}`);
    }

    // Test 4: Check cache behavior
    addResult(`🗄️ Cache Info - Valid: ${debugInfo?.cacheValid}, Last Check: ${debugInfo?.lastSuccessfulCheck}`);

    // Test 5: Verify state consistency
    const hookSubscribed = subscribed;
    const storeSubscribed = subscriptionInfo.subscribed;
    if (hookSubscribed === storeSubscribed) {
      addResult('✅ Hook and store subscription states are consistent');
    } else {
      addResult(`❌ State inconsistency - Hook: ${hookSubscribed}, Store: ${storeSubscribed}`);
    }

    addResult('🏁 Tests completed');
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Subscription Status Test Component</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Current Status</h4>
              <div className="text-sm space-y-1">
                <div>Subscribed: <span className="font-mono">{String(subscribed)}</span></div>
                <div>Loading: <span className="font-mono">{String(loading)}</span></div>
                <div>Error: <span className="font-mono">{error || 'None'}</span></div>
                <div>Tier: <span className="font-mono">{subscription_tier || 'None'}</span></div>
                <div>In Trial: <span className="font-mono">{String(in_trial)}</span></div>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">Store State</h4>
              <div className="text-sm space-y-1">
                <div>Subscribed: <span className="font-mono">{String(subscriptionInfo.subscribed)}</span></div>
                <div>Tier: <span className="font-mono">{subscriptionInfo.subscription_tier || 'None'}</span></div>
                <div>In Trial: <span className="font-mono">{String(subscriptionInfo.in_trial)}</span></div>
                <div>Monthly Created: <span className="font-mono">{subscriptionInfo.monthlyTutorialsCreated}</span></div>
                <div>Monthly Limit: <span className="font-mono">{subscriptionInfo.maxTutorialsPerMonth}</span></div>
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={runTests} disabled={loading}>
              Run Tests
            </Button>
            <Button onClick={() => refetchSubscription(true)} disabled={loading} variant="outline">
              Force Refresh
            </Button>
            <Button onClick={clearResults} variant="outline">
              Clear Results
            </Button>
          </div>
        </CardContent>
      </Card>

      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-100 p-4 rounded-md max-h-64 overflow-y-auto">
              <pre className="text-sm whitespace-pre-wrap">
                {testResults.join('\n')}
              </pre>
            </div>
          </CardContent>
        </Card>
      )}

      {debugInfo && (
        <Card>
          <CardHeader>
            <CardTitle>Debug Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm space-y-1">
              <div>Last Successful Check: <span className="font-mono">{debugInfo.lastSuccessfulCheck}</span></div>
              <div>Cache Valid: <span className="font-mono">{String(debugInfo.cacheValid)}</span></div>
              <div>Retry Count: <span className="font-mono">{debugInfo.retryCount}</span></div>
              <div>Is Checking: <span className="font-mono">{String(debugInfo.isChecking)}</span></div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SubscriptionStatusTest;
