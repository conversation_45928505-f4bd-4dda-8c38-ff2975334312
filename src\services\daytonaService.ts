/**
 * Daytona Service - Manages sandbox creation and code execution
 * Provides both demo mode and real API integration capabilities
 */

import { Daytona, Sandbox, DaytonaConfig, CreateSandboxParams, DaytonaError } from '@daytonaio/sdk';

interface SandboxInfo {
  id: string;
  language: string;
}

interface ExecutionResult {
  success: boolean;
  result?: string;
  output?: string;
  error?: string | null;
  executionTime?: number;
  isDemo?: boolean;
  charts?: any[]; // For matplotlib charts from Python execution
}

interface SandboxCreationResult {
  success: boolean;
  sandboxId?: string;
  message: string;
  error?: string;
}

interface SandboxDetails {
  id: string;
  state: string;
  resources: {
    cpu: string;
    memory: string;
  };
  createdAt: Date;
}

interface ServiceStatus {
  initialized: boolean;
  hasSandbox: boolean;
  isDemo: boolean;
}

class DaytonaService {
  private currentSandbox: SandboxInfo | null;
  private isInitialized: boolean;
  private daytonaClient: Daytona | null;
  private activeSandbox: Sandbox | null;
  private isDemoMode: boolean;

  constructor() {
    this.currentSandbox = null;
    this.isInitialized = false;
    this.daytonaClient = null;
    this.activeSandbox = null;
    this.isDemoMode = true;
  }

  /**
   * Initialize the Daytona client
   * @param apiKey - Optional API key for Daytona service
   * @param apiUrl - Optional API URL (defaults to https://app.daytona.io/api)
   * @param target - Optional target region (us, eu, asia)
   * @returns Promise<boolean> - Success status
   */
  async initialize(
    apiKey: string | null = null,
    apiUrl?: string,
    target?: string
  ): Promise<boolean> {
    try {
      // Check for API key in environment variables if not provided
      const effectiveApiKey = apiKey || process.env.DAYTONA_API_KEY;

      if (!effectiveApiKey) {
        console.warn('Daytona API key not configured. Using demo mode.');
        this.isDemoMode = true;
        this.isInitialized = false;
        return false;
      }

      // Configure Daytona client
      const config: DaytonaConfig = {
        apiKey: effectiveApiKey,
        apiUrl: apiUrl || process.env.DAYTONA_API_URL || 'https://app.daytona.io/api',
        target: (target || process.env.DAYTONA_TARGET || 'us') as any
      };

      this.daytonaClient = new Daytona(config);
      this.isDemoMode = false;
      this.isInitialized = true;

      console.log('Daytona client initialized successfully');
      return true;
    } catch (error) {
      console.error('Error initializing Daytona:', error);
      this.isDemoMode = true;
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * Create a new sandbox
   * @param language - Programming language for the sandbox
   * @param resources - Optional resource allocation
   * @param envVars - Optional environment variables
   * @returns Promise<SandboxCreationResult>
   */
  async createSandbox(
    language: string = 'javascript',
    resources?: { cpu?: number; memory?: number; disk?: number },
    envVars?: Record<string, string>
  ): Promise<SandboxCreationResult> {
    try {
      // Use demo mode if not initialized with real API
      if (!this.isInitialized || this.isDemoMode) {
        return this.createDemoSandbox(language);
      }

      if (!this.daytonaClient) {
        throw new Error('Daytona client not initialized');
      }

      // Configure sandbox parameters
      const params: CreateSandboxParams = {
        language: language,
        envVars: envVars || {},
        autoStopInterval: 60, // Auto-stop after 1 hour of inactivity
        timeout: 120 // 2 minutes timeout for creation
      };

      // Add resource allocation if specified
      if (resources) {
        params.resources = {
          cpu: resources.cpu || 1,
          memory: resources.memory || 2,
          disk: resources.disk || 10
        };
      }

      console.log('Creating Daytona sandbox with params:', params);

      // Create the sandbox
      this.activeSandbox = await this.daytonaClient.create(params, 120);

      this.currentSandbox = {
        id: this.activeSandbox.id,
        language: language
      };

      console.log(`Sandbox created successfully: ${this.activeSandbox.id}`);

      return {
        success: true,
        sandboxId: this.activeSandbox.id,
        message: `${language} sandbox created successfully`
      };
    } catch (error) {
      console.error('Error creating sandbox:', error);

      // Fallback to demo mode on error
      if (error instanceof DaytonaError) {
        console.warn('Daytona API error, falling back to demo mode:', error.message);
        return this.createDemoSandbox(language);
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Error creating sandbox'
      };
    }
  }

  /**
   * Create a demo sandbox for testing purposes
   * @param language - Programming language
   * @returns SandboxCreationResult
   */
  private createDemoSandbox(language: string): SandboxCreationResult {
    this.currentSandbox = {
      id: `demo_sandbox_${Date.now()}`,
      language: language
    };

    return {
      success: true,
      sandboxId: this.currentSandbox.id,
      message: `${language} demo sandbox created successfully`
    };
  }

  /**
   * Execute code in the sandbox
   * @param code - Code to execute
   * @param language - Programming language
   * @param timeout - Execution timeout in seconds
   * @returns Promise<ExecutionResult>
   */
  async executeCode(
    code: string,
    language: string = 'javascript',
    timeout: number = 30
  ): Promise<ExecutionResult> {
    const startTime = Date.now();

    try {
      // Use demo mode if not initialized with real API or no active sandbox
      if (!this.isInitialized || this.isDemoMode || !this.activeSandbox) {
        return this.simulateExecution(code, language);
      }

      console.log(`Executing ${language} code in sandbox ${this.activeSandbox.id}`);

      // Execute code using the sandbox's process interface
      const response = await this.activeSandbox.process.codeRun(code, {}, timeout);

      const executionTime = Date.now() - startTime;

      // Handle successful execution
      if (response.exitCode === 0) {
        return {
          success: true,
          result: response.artifacts?.stdout || response.result || '',
          output: response.artifacts?.stdout || response.result || '',
          error: null,
          executionTime,
          isDemo: false,
          charts: response.artifacts?.charts || []
        };
      } else {
        // Handle execution error
        return {
          success: false,
          result: '',
          output: response.artifacts?.stdout || '',
          error: `Code execution failed with exit code ${response.exitCode}`,
          executionTime,
          isDemo: false
        };
      }
    } catch (error) {
      console.error('Error executing code:', error);

      // Fallback to demo mode on error
      if (error instanceof DaytonaError) {
        console.warn('Daytona API error, falling back to demo mode:', error.message);
        return this.simulateExecution(code, language);
      }

      const executionTime = Date.now() - startTime;
      return {
        success: false,
        result: '',
        output: '',
        error: error instanceof Error ? error.message : 'Unknown execution error',
        executionTime,
        isDemo: false
      };
    }
  }

  /**
   * Simulate code execution for demo mode
   * @param code - Code to execute
   * @param language - Programming language
   * @returns ExecutionResult
   */
  private simulateExecution(code: string, language: string): ExecutionResult {
    const examples: Record<string, Record<string, string>> = {
      javascript: {
        'console.log("Hello World!")': 'Hello World!',
        'console.log("Hello from Manus AI!")': 'Hello from Manus AI!',
        'function HelloWorld() {\n  return <div>Hello from Manus AI!</div>\n}': 'React component created successfully',
        'const sum = (a, b) => a + b;\nconsole.log(sum(2, 3));': '5',
        'console.log(Math.PI);': '3.141592653589793',
        'console.log(new Date().getFullYear());': new Date().getFullYear().toString()
      },
      python: {
        'print("Hello World!")': 'Hello World!',
        'print("Hello from Manus AI!")': 'Hello from Manus AI!',
        'print(2 + 3)': '5',
        'import math\nprint(math.pi)': '3.141592653589793',
        'print([i**2 for i in range(5)])': '[0, 1, 4, 9, 16]'
      },
      typescript: {
        'console.log("Hello TypeScript!")': 'Hello TypeScript!',
        'const greeting: string = "Hello";\nconsole.log(greeting);': 'Hello',
        'interface User { name: string; }\nconst user: User = { name: "John" };\nconsole.log(user.name);': 'John'
      },
      java: {
        'System.out.println("Hello World!");': 'Hello World!',
        'System.out.println("Hello from Manus AI!");': 'Hello from Manus AI!'
      },
      cpp: {
        '#include <iostream>\nstd::cout << "Hello World!" << std::endl;': 'Hello World!',
        'std::cout << 2 + 3 << std::endl;': '5'
      }
    };

    const languageExamples = examples[language] || examples.javascript;
    const result = languageExamples[code] || `${language} code executed successfully (demo mode)`;

    return {
      success: true,
      result: result,
      output: result,
      error: null,
      executionTime: Math.random() * 1000 + 500, // Simulate execution time
      isDemo: true
    };
  }

  /**
   * Get information about the current sandbox
   * @returns Promise<SandboxDetails | null>
   */
  async getSandboxInfo(): Promise<SandboxDetails | null> {
    if (!this.currentSandbox) {
      return null;
    }

    try {
      // Return demo info if in demo mode or no active sandbox
      if (this.isDemoMode || !this.activeSandbox) {
        return {
          id: this.currentSandbox.id,
          state: 'running',
          resources: { cpu: '1 core', memory: '2GB' },
          createdAt: new Date()
        };
      }

      // Get real sandbox info from Daytona API
      const info = await this.activeSandbox.info();

      return {
        id: info.id,
        state: info.state,
        resources: {
          cpu: info.resources.cpu,
          memory: info.resources.memory
        },
        createdAt: new Date(info.updatedAt)
      };
    } catch (error) {
      console.error('Error getting sandbox info:', error);

      // Fallback to basic info
      return {
        id: this.currentSandbox.id,
        state: 'unknown',
        resources: { cpu: '1 core', memory: '2GB' },
        createdAt: new Date()
      };
    }
  }

  /**
   * Clean up resources
   * @param deleteSandbox - Whether to delete the sandbox from Daytona
   * @returns Promise<void>
   */
  async cleanup(deleteSandbox: boolean = false): Promise<void> {
    try {
      if (deleteSandbox && this.activeSandbox && !this.isDemoMode) {
        console.log(`Deleting sandbox: ${this.activeSandbox.id}`);
        await this.activeSandbox.delete();
      }
    } catch (error) {
      console.error('Error deleting sandbox:', error);
    } finally {
      this.currentSandbox = null;
      this.activeSandbox = null;
    }
  }

  /**
   * Stop the current sandbox
   * @returns Promise<boolean>
   */
  async stopSandbox(): Promise<boolean> {
    try {
      if (!this.activeSandbox || this.isDemoMode) {
        return true; // Demo mode or no sandbox
      }

      console.log(`Stopping sandbox: ${this.activeSandbox.id}`);
      await this.activeSandbox.stop();
      return true;
    } catch (error) {
      console.error('Error stopping sandbox:', error);
      return false;
    }
  }

  /**
   * Start the current sandbox
   * @returns Promise<boolean>
   */
  async startSandbox(): Promise<boolean> {
    try {
      if (!this.activeSandbox || this.isDemoMode) {
        return true; // Demo mode or no sandbox
      }

      console.log(`Starting sandbox: ${this.activeSandbox.id}`);
      await this.activeSandbox.start();
      return true;
    } catch (error) {
      console.error('Error starting sandbox:', error);
      return false;
    }
  }

  /**
   * Check if the service is ready
   * @returns boolean
   */
  isReady(): boolean {
    return this.isInitialized || this.isDemoMode;
  }

  /**
   * Get the current service status
   * @returns ServiceStatus
   */
  getStatus(): ServiceStatus {
    return {
      initialized: this.isInitialized,
      hasSandbox: !!this.currentSandbox,
      isDemo: this.isDemoMode
    };
  }

  /**
   * Get the preview URL for a port in the sandbox
   * @param port - Port number
   * @returns Promise<string | null>
   */
  async getPreviewUrl(port: number): Promise<string | null> {
    try {
      if (!this.activeSandbox || this.isDemoMode) {
        return `http://localhost:${port}`; // Demo URL
      }

      const previewLink = await this.activeSandbox.getPreviewLink(port);
      return previewLink.url;
    } catch (error) {
      console.error('Error getting preview URL:', error);
      return null;
    }
  }
}

// Export a singleton instance
export const daytonaService = new DaytonaService();
export default daytonaService;