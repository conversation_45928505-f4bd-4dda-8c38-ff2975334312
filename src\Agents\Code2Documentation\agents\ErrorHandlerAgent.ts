// src/Agents/Code2Documentation/pangeaflow/agents/ErrorHandlerAgent.ts

import { AgentComponent, ExecutionContext, ExecutionResult } from '@/pangeaflow';
import { emitGraphStatus, emitError } from '../../shared/events';

export class ErrorHandlerAgent extends AgentComponent {
  constructor(eventBus: any, telemetry: any) {
    super('error-handler', eventBus, telemetry);
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const error = context.sharedState.error as Error || new Error("Unknown error occurred");
    
    emitError("Code2Documentation", error.message, error);
    emitGraphStatus("ErrorHandler", 100, `Error: ${error.message}`);
    
    // Log error details
    console.error("Code2Documentation workflow error:", error);
    
    // Return error result
    return {
      success: false,
      error,
      events: [],
      nextActions: [],
      sharedStateUpdates: context.sharedState
    };
  }
}
