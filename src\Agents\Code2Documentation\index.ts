// src/Agents/Code2Documentation/pangeaflow/index.ts

/**
 * Code2Documentation PangeaFlow Implementation
 * 
 * This module provides a PangeaFlow-based implementation of the Code2Documentation system,
 * offering improved error handling, progress reporting, and workflow orchestration
 * compared to the original PocketFlow implementation.
 * 
 * Key Features:
 * - Event-driven workflow orchestration
 * - Comprehensive error handling with retry logic
 * - Detailed progress reporting and telemetry
 * - Modular agent-based architecture
 * - Built-in state management
 */

// Main workflow exports
export { 
  createCode2DocumentationWorkflow, 
  executeCode2DocumentationWorkflow,
  createDefaultSharedStore,
  validateSharedStore
} from './flow/pangeaFlow';

// Agent components
export * from './agents';

// Utilities
export * from '../shared/events';


// Re-export shared types
export * from './types';
