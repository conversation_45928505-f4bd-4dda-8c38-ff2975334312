import React, { useEffect, useState } from 'react';
import { useSEO, defaultSEO } from '@/hooks/seo/useSEO';
import { DashBoardTitle } from '@/components/layouts/DashboardTitle';
import TutorialChatInterface from '@/components/chat/TutorialChatInterface';
import { useAuth } from '@/hooks/useAuth';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';
import { useTutorials } from '@/hooks/useTutorials';

const ChatWithTutorial: React.FC = () => {
 
  const {
    tutorials,
    loading: isLoading,
    error,
  } = useTutorials();
  // Apply SEO
  useSEO({
    ...defaultSEO,
    title: 'Chat with Tutorial - CodeTutorPro | Interactive Learning Assistant',
    description: 'Get instant help and explanations about your tutorials with our AI-powered chat assistant. Ask questions, clarify concepts, and enhance your learning experience.',
    keywords: 'tutorial chat, AI learning assistant, interactive tutorials, code help, programming questions, tutorial explanations',
    canonical: 'https://codetutorpro.com/dashboard/chat-with-tutorial',
  });

 

  if (isLoading) {
    return (
      <div className="space-y-6">
        <DashBoardTitle 
          title="Chat with Tutorial" 
          description="Get instant help and explanations about your tutorials"
        />
        
        <div className="space-y-4">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 h-full flex flex-col">
      <DashBoardTitle 
        title="Chat with Tutorial" 
        description="Get instant help and explanations about your tutorials with our AI assistant"
      />

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}. Showing demo tutorials for now.
          </AlertDescription>
        </Alert>
      )}

      <div className="flex-1 min-h-0">
        <TutorialChatInterface 
          availableTutorials={tutorials}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
};

export default ChatWithTutorial;
