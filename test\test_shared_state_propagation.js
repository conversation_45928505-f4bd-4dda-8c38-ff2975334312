// This file has been converted to a React TypeScript component
// See: src/pages/SharedStatePropagationTest.tsx

// Mock execution context for testing
function createMockContext(initialSharedState = {}) {
  return {
    sharedState: initialSharedState,
    nodeOutputs: new Map(),
    events: []
  };
}

// Mock execution result for testing
function createMockResult(sharedStateUpdates = {}) {
  return {
    success: true,
    output: { test: 'data' },
    events: [],
    nextActions: ['next-action'],
    sharedStateUpdates
  };
}

// Test 1: PangeaFlow state merging logic
function testPangeaFlowStateMerging() {
  console.log('\n📋 Test 1: PangeaFlow shared state merging');
  
  try {
    // Simulate initial context
    const context = createMockContext({
      tutorial_id: 'test-tutorial-123',
      user_id: 'test-user',
      project_name: 'Test Project'
    });
    
    console.log('Initial context.sharedState:', context.sharedState);
    
    // Simulate agent result with state updates
    const result = createMockResult({
      files: [['main.js', 'console.log("hello");']],
      primaryLanguage: 'javascript',
      fileCount: 1
    });
    
    console.log('Agent sharedStateUpdates:', result.sharedStateUpdates);
    
    // Simulate the PangeaFlow merging logic
    if (result.sharedStateUpdates && Object.keys(result.sharedStateUpdates).length > 0) {
      const updatedSharedState = {
        ...context.sharedState,
        ...result.sharedStateUpdates
      };
      
      // Update context (simulating the fix)
      context.sharedState = updatedSharedState;
      
      console.log('Merged context.sharedState:', context.sharedState);
      
      // Verify all expected fields are present
      const expectedFields = ['tutorial_id', 'user_id', 'project_name', 'files', 'primaryLanguage', 'fileCount'];
      const missingFields = expectedFields.filter(field => !(field in context.sharedState));
      
      if (missingFields.length > 0) {
        throw new Error(`Missing fields after merge: ${missingFields.join(', ')}`);
      }
      
      // Verify original fields are preserved
      if (context.sharedState.tutorial_id !== 'test-tutorial-123') {
        throw new Error('tutorial_id was not preserved during merge');
      }
      
      if (context.sharedState.user_id !== 'test-user') {
        throw new Error('user_id was not preserved during merge');
      }
      
      // Verify new fields are added
      if (!context.sharedState.files || context.sharedState.files.length === 0) {
        throw new Error('files were not added during merge');
      }
      
      console.log('✅ Test 1 PASSED: State merging works correctly');
      return true;
    } else {
      throw new Error('No sharedStateUpdates to merge');
    }
  } catch (error) {
    console.error('❌ Test 1 FAILED:', error);
    return false;
  }
}

// Test 2: Agent state preservation pattern
function testAgentStatePreservation() {
  console.log('\n📋 Test 2: Agent state preservation pattern');
  
  try {
    // Simulate context with existing shared state
    const context = createMockContext({
      tutorial_id: 'test-tutorial-456',
      user_id: 'test-user',
      project_name: 'Another Project',
      files: [['existing.js', 'existing code']],
      primaryLanguage: 'javascript'
    });
    
    console.log('Context before agent execution:', context.sharedState);
    
    // Simulate agent adding new data while preserving existing state
    const newData = {
      concepts: ['variables', 'functions', 'loops'],
      conceptsExtracted: 3,
      targetAudience: 'beginner'
    };
    
    // This is the pattern all agents should follow
    const agentStateUpdates = {
      ...context.sharedState,  // ✅ Preserve existing state
      ...newData               // ✅ Add new data
    };
    
    console.log('Agent state updates:', agentStateUpdates);
    
    // Verify preservation
    const originalFields = ['tutorial_id', 'user_id', 'project_name', 'files', 'primaryLanguage'];
    const newFields = ['concepts', 'conceptsExtracted', 'targetAudience'];
    
    // Check original fields are preserved
    for (const field of originalFields) {
      if (agentStateUpdates[field] !== context.sharedState[field]) {
        throw new Error(`Original field ${field} was not preserved`);
      }
    }
    
    // Check new fields are added
    for (const field of newFields) {
      if (!(field in agentStateUpdates)) {
        throw new Error(`New field ${field} was not added`);
      }
    }
    
    console.log('✅ Test 2 PASSED: Agent state preservation pattern works');
    return true;
  } catch (error) {
    console.error('❌ Test 2 FAILED:', error);
    return false;
  }
}

// Test 3: End-to-end workflow state accumulation
function testWorkflowStateAccumulation() {
  console.log('\n📋 Test 3: End-to-end workflow state accumulation');
  
  try {
    // Simulate workflow execution through multiple agents
    let context = createMockContext({
      tutorial_id: 'test-tutorial-789',
      user_id: 'test-user',
      target_audience: 'beginner'
    });
    
    console.log('Initial workflow state:', context.sharedState);
    
    // Agent 1: RepoAnalysisAgent
    const repoResult = {
      ...context.sharedState,
      files: [['main.js', 'code'], ['utils.js', 'utilities']],
      primaryLanguage: 'javascript',
      project_name: 'Test Project'
    };
    context.sharedState = repoResult;
    console.log('After RepoAnalysisAgent:', Object.keys(context.sharedState));
    
    // Agent 2: ConceptExtractionAgent
    const conceptResult = {
      ...context.sharedState,
      concepts: ['variables', 'functions'],
      conceptsExtracted: 2
    };
    context.sharedState = conceptResult;
    console.log('After ConceptExtractionAgent:', Object.keys(context.sharedState));
    
    // Agent 3: TutorialPlanningAgent
    const planResult = {
      ...context.sharedState,
      tutorial_structure: { sections: ['intro', 'basics', 'advanced'] },
      sectionsPlanned: 3
    };
    context.sharedState = planResult;
    console.log('After TutorialPlanningAgent:', Object.keys(context.sharedState));
    
    // Agent 4: ContentGenerationAgent
    const contentResult = {
      ...context.sharedState,
      sections: [{ title: 'Intro', content: 'Welcome...' }],
      sectionsGenerated: 1
    };
    context.sharedState = contentResult;
    console.log('After ContentGenerationAgent:', Object.keys(context.sharedState));
    
    // Verify final state has all accumulated data
    const expectedFields = [
      'tutorial_id', 'user_id', 'target_audience',  // Initial
      'files', 'primaryLanguage', 'project_name',   // RepoAnalysis
      'concepts', 'conceptsExtracted',               // ConceptExtraction
      'tutorial_structure', 'sectionsPlanned',      // TutorialPlanning
      'sections', 'sectionsGenerated'               // ContentGeneration
    ];
    
    const missingFields = expectedFields.filter(field => !(field in context.sharedState));
    
    if (missingFields.length > 0) {
      throw new Error(`Missing fields in final state: ${missingFields.join(', ')}`);
    }
    
    // Verify original tutorial_id is preserved
    if (context.sharedState.tutorial_id !== 'test-tutorial-789') {
      throw new Error('tutorial_id was lost during workflow execution');
    }
    
    console.log('Final workflow state keys:', Object.keys(context.sharedState));
    console.log('✅ Test 3 PASSED: Workflow state accumulation works correctly');
    return true;
  } catch (error) {
    console.error('❌ Test 3 FAILED:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Code2Tutor shared state propagation tests...\n');
  
  const results = [
    testPangeaFlowStateMerging(),
    testAgentStatePreservation(),
    testWorkflowStateAccumulation()
  ];
  
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  console.log(`\n📊 Test Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests PASSED! The shared state propagation fix is working correctly.');
    console.log('\n📝 Next steps:');
    console.log('1. Test the actual Code2Tutor workflow');
    console.log('2. Verify that AnalyzeRelationshipsAgent receives the expected shared state');
    console.log('3. Check that tutorial_id, files, and project_name are properly propagated');
  } else {
    console.log('⚠️ Some tests FAILED. Please check the implementation.');
  }
  
  return passed === total;
}

// Export for manual testing
window.testSharedStatePropagation = runAllTests;

console.log('📝 Test script loaded. Run window.testSharedStatePropagation() to execute tests.');
